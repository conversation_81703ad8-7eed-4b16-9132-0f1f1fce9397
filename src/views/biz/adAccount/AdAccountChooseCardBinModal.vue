<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 600 ? 600 : '100%'"
    draggable
    @before-ok="save"
    @close="reset"
  >
    <a-spin :loading="loading" style="width: 100%">
      <GiForm ref="formRef" v-model="form" :options="options" :columns="columns" />
    </a-spin>
  </a-modal>
</template>

<script setup lang="tsx">
import { useWindowSize } from '@vueuse/core'
import { type Columns, GiForm, type Options } from '@/components/GiForm'
import { useLoading, useResetReactive } from '@/hooks'
import type { LabelValueState } from '@/types/global'
import { checkUserOpenCardPerms, getCardBinList } from '@/apis/biz/card'
import { useDict } from '@/hooks/app'

const emit = defineEmits<{
  (e: 'success', data: any): void
}>()

const { width } = useWindowSize()

const visible = ref(false)
const title = computed(() => ('选择卡头'))
const formRef = ref<InstanceType<typeof GiForm>>()
const binList = ref<LabelValueState[]>([])

const { loading, setLoading } = useLoading(false)

const options: Options = {
  form: { size: 'large' },
  btns: { hide: true },
}

const [form, resetForm] = useResetReactive({
  binId: undefined,
  cardBin: undefined,
  platform: 2,
  cardScheme: undefined,
})

// 获取平台字典
const { card_platform } = useDict('card_platform')
const availablePlatforms = ref<number[]>([])

const columns: Columns = reactive([
  {
    label: '平台',
    field: 'platform',
    type: 'select',
    options: computed(() => {
      if (!card_platform?.value) return []
      return card_platform.value.filter((item) => availablePlatforms.value.includes(Number(item.value)))
    }),
    defaultValue: 2,
    rules: [{ required: true, message: '请选择平台' }],
    props: {
      onChange: (value: number) => {
        form.cardBin = undefined
        form.cardScheme = undefined
        formRef.value?.formRef?.clearValidate()
        getBinList(value)
      },
    },
  },
  {
    label: '卡头',
    field: 'binId',
    type: 'radio-group',
    options: binList,
    rules: [{ required: true, message: '请选择卡头' }],
    props: {
      onChange: (value: string) => {
        // 找到选中的卡头选项
        const selectedBin = binList.value.find((item) => item.value === value)
        // 设置对应的 cardScheme
        form.cardScheme = selectedBin?.cardScheme
      },
    },
  },
])

// 重置
const reset = () => {
  formRef.value?.formRef?.resetFields()
  resetForm()
}

// 保存
const save = async () => {
  try {
    const isInvalid = await formRef.value?.formRef?.validate()
    if (isInvalid) return false
    emit('success', form)
    return true
  } catch (error) {
    return false
  }
}

const getBinList = async (platform: number) => {
  setLoading(true)
  try {
    const { data } = await getCardBinList(platform)
    if (platform === 3) { // 光子易平台
      binList.value = data.flatMap((item) => {
        if (!item.extra) return [item]
        const cardSchemes = item.extra.cardScheme.split(',')
        if (cardSchemes.length === 1) {
          return [{
            ...item,
            label: `${item.label}(${item.extra.cardScheme},已开卡数:${item.extra.availableCard}`,
            cardScheme: item.extra.cardScheme,
          }]
        }

        // 处理多个 cardScheme 的情况
        return cardSchemes.map((scheme) => ({
          ...item,
          label: `${item.label}(${scheme},已开卡数:${item.extra.availableCard}/剩余开卡数:${item.extra.remainingAvailableCard})`,
          cardScheme: scheme,
        }))
      })
    } else if (platform === 2) {
      binList.value = data.filter((item) => {
        // 检查 value 字段是否包含 531993
        const cardBin = String(item.value || '')
        return !cardBin.includes('531993')
      })
    } else {
      binList.value = data
    }
  } catch (error) {
    console.error('获取卡头列表失败:', error)
  } finally {
    setLoading(false)
  }
}

// 修改 onOpen 方法
const onOpen = async () => {
  reset()
  visible.value = true
  try {
    const { data } = await checkUserOpenCardPerms()
    availablePlatforms.value = data
    // 如果默认平台不在可用平台列表中，需要重置
    if (!availablePlatforms.value.includes(form.platform)) {
      form.platform = availablePlatforms.value[0]
    }
    await getBinList(form.platform)
  } catch (error) {
    console.error('获取开卡权限失败:', error)
  }
}

defineExpose({ onOpen })
</script>

<style scoped lang="scss"></style>
