<template>
  <div class="table-page">
    <GiTable
      title="卡头管理管理"
      row-key="id"
      :data="dataList"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
      :pagination="pagination"
      :disabled-tools="['size']"
      :disabled-column-keys="['name']"
      @refresh="search"
    >
      <template #toolbar-left>
	    <a-input-search v-model="queryForm.cardBin" placeholder="请输入卡头号码" allow-clear @search="search" />
	    <a-input-search v-model="queryForm.name" placeholder="请输入卡头名称" allow-clear @search="search" />
        <a-select
          v-model="queryForm.platform"
          :options="platform_enum"
          placeholder="请选择所属平台"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-model="queryForm.cardScheme"
          :options="card_scheme_enum"
          placeholder="请选择卡组织"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-model="queryForm.enable"
          :options="enable_enum"
          placeholder="请选择是否启用"
          allow-clear
          style="width: 150px"
          @change="search"
        />
	    <a-input-search v-model="queryForm.createTime" placeholder="请输入创建时间" allow-clear @search="search" />
        <a-button @click="reset">
          <template #icon><icon-refresh /></template>
          <template #default>重置</template>
        </a-button>
      </template>
      <template #toolbar-right>
        <a-button v-permission="['biz:cardBin:add']" type="primary" @click="onAdd">
          <template #icon><icon-plus /></template>
          <template #default>新增</template>
        </a-button>
        <a-button v-permission="['biz:cardBin:export']" @click="onExport">
          <template #icon><icon-download /></template>
          <template #default>导出</template>
        </a-button>
      </template>
      <template #platform="{ record }">
        <GiCellTag :value="record.platform" :dict="card_platform" />
      </template>
      <template #action="{ record }">
        <a-space>
          <a-link v-permission="['biz:cardBin:detail']" title="详情" @click="onDetail(record)">详情</a-link>
          <a-link v-permission="['biz:cardBin:update']" title="修改" @click="onUpdate(record)">修改</a-link>
          <a-link
            v-permission="['biz:cardBin:delete']"
            status="danger"
            :disabled="record.disabled"
            :title="record.disabled ? '不可删除' : '删除'"
            @click="onDelete(record)"
          >
            删除
          </a-link>
        </a-space>
      </template>
    </GiTable>

    <CardBinAddModal ref="CardBinAddModalRef" @save-success="search" />
    <CardBinDetailDrawer ref="CardBinDetailDrawerRef" />
  </div>
</template>

<script setup lang="ts">
import CardBinAddModal from './CardBinAddModal.vue'
import CardBinDetailDrawer from './CardBinDetailDrawer.vue'
import { type CardBinResp, type CardBinQuery, deleteCardBin, exportCardBin, listCardBin } from '@/apis/biz/cardBin'
import type { TableInstanceColumns } from '@/components/GiTable/type'
import { useDownload, useTable } from '@/hooks'
import { useDict } from '@/hooks/app'
import { isMobile } from '@/utils'
import has from '@/utils/has'

defineOptions({ name: 'CardBin' })

const { card_platform } = useDict('card_platform')

const queryForm = reactive<CardBinQuery>({
  cardBin: undefined,
  name: undefined,
  platform: undefined,
  cardScheme: undefined,
  enable: undefined,
  createTime: undefined,
  sort: ['id,desc']
})

const {
  tableData: dataList,
  loading,
  pagination,
  search,
  handleDelete
} = useTable((page) => listCardBin({ ...queryForm, ...page }), { immediate: true })
const columns = ref<TableInstanceColumns[]>([
  { title: 'ID', dataIndex: 'id', slotName: 'id' },
  { title: '卡头号码', dataIndex: 'cardBin', slotName: 'cardBin' },
  { title: '卡头名称', dataIndex: 'name', slotName: 'name' },
  { title: '所属平台', dataIndex: 'platform', slotName: 'platform' },
  { title: '卡组织', dataIndex: 'cardScheme', slotName: 'cardScheme' },
  { title: '是否启用', dataIndex: 'enable', slotName: 'enable' },
  { title: '创建时间', dataIndex: 'createTime', slotName: 'createTime' },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 160,
    align: 'center',
    fixed: !isMobile() ? 'right' : undefined,
    show: has.hasPermOr(['biz:cardBin:detail', 'biz:cardBin:update', 'biz:cardBin:delete'])
  }
]);

// 重置
const reset = () => {
  queryForm.cardBin = undefined
  queryForm.name = undefined
  queryForm.platform = undefined
  queryForm.cardScheme = undefined
  queryForm.enable = undefined
  queryForm.createTime = undefined
  search()
}

// 删除
const onDelete = (record: CardBinResp) => {
  return handleDelete(() => deleteCardBin(record.id), {
    content: `是否确定删除该条数据？`,
    showModal: true
  })
}

// 导出
const onExport = () => {
  useDownload(() => exportCardBin(queryForm))
}

const CardBinAddModalRef = ref<InstanceType<typeof CardBinAddModal>>()
// 新增
const onAdd = () => {
  CardBinAddModalRef.value?.onAdd()
}

// 修改
const onUpdate = (record: CardBinResp) => {
  CardBinAddModalRef.value?.onUpdate(record.id)
}

const CardBinDetailDrawerRef = ref<InstanceType<typeof CardBinDetailDrawer>>()
// 详情
const onDetail = (record: CardBinResp) => {
  CardBinDetailDrawerRef.value?.onOpen(record.id)
}
</script>

<style scoped lang="scss"></style>
