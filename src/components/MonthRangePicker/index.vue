<template>
  <a-range-picker
    mode="month"
    :placeholder="placeholder"
    format="YYYY-MM"
    value-format="YYYY-MM"
    style="height: 32px"
    :allow-clear="allowClear"
  />
</template>

<script setup lang="ts">
defineOptions({ name: 'MonthRangePicker' })

defineProps({
  placeholder: {
    type: Array as PropType<string[]>,
    default: (): string[] => ['开始时间', '结束时间'],
  },

  allowClear: {
    type: Boolean,
    default: true,
  },
})
</script>

<style scoped lang="less"></style>
