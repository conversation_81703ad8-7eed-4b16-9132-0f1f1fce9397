<template>
  <a-range-picker
    :placeholder="placeholder"
    :format="format"
    :value-format="format"
    :show-time="showTime"
    :shortcuts="shortcuts"
    shortcuts-position="left"
    :time-picker-props="{
      defaultValue: ['00:00:00', '23:59:59'],
    }"
    style="height: 32px"
    :allow-clear="allowClear"
  />
</template>

<script setup lang="ts">
import type { ShortcutType } from '@arco-design/web-vue'
import dayjs from 'dayjs'

defineOptions({ name: 'DateRangePicker' })

defineProps({
  format: {
    type: String,
    default: 'YYYY-MM-DD HH:mm:ss',
  },
  showTime: {
    type: Boolean,
    default: true,
  },
  placeholder: {
    type: Array as PropType<string[]>,
    default: (): string[] => ['开始时间', '结束时间'],
  },

  allowClear: {
    type: Boolean,
    default: true,
  },
})

const shortcuts = computed<ShortcutType[]>(() => {
  return [
    {
      label: '今天',
      value: (): Date[] => [dayjs().startOf('day').toDate(), dayjs().toDate()],
    },
    {
      label: '昨天',
      value: (): Date[] => [
        dayjs().subtract(1, 'day').startOf('day').toDate(),
        dayjs().subtract(1, 'day').endOf('day').toDate(),
      ],
    },
    {
      label: '本周',
      value: (): Date[] => [dayjs().startOf('week').add(1, 'day').toDate(), dayjs().toDate()],
    },
    {
      label: '上周',
      value: (): Date[] => [
        dayjs().subtract(1, 'week').startOf('week').add(1, 'day').toDate(),
        dayjs().subtract(1, 'week').endOf('week').add(1, 'day').toDate(),
      ],
    },
    {
      label: '本月',
      value: (): Date[] => [dayjs().startOf('month').toDate(), dayjs().toDate()],
    },
    {
      label: '上月',
      value: (): Date[] => [
        dayjs().subtract(1, 'month').startOf('month').toDate(),
        dayjs().subtract(1, 'month').endOf('month').toDate(),
      ],
    },
    {
      label: '本年',
      value: (): Date[] => [dayjs().startOf('year').toDate(), dayjs().toDate()],
    },
  ]
})
</script>

<style scoped lang="less"></style>
