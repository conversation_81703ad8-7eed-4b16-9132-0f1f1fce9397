<template>
  <a-select
    :placeholder="placeholder"
    :options="userList"
    allow-clear
    allow-search
    style="width: 150px"
  />
</template>

<script setup lang="ts">
import type { LabelValueState } from '@/types/global'
import { listUserDict } from '@/apis'

defineOptions({ name: 'BusinessUserSelect' })

withDefaults(defineProps<Props>(), {
  placeholder: '请选择商务',
})

interface Props {
  placeholder?: string
}

const userList = ref<LabelValueState[]>([])
const getUserList = async () => {
  const { data } = await listUserDict({ deptId: '702164640200656226' })
  userList.value = data
}
onMounted(() => {
  getUserList()
})
</script>

<style scoped lang="less"></style>
