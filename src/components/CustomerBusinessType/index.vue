<script setup lang="ts">
import {useDict} from "@/hooks/app";

defineOptions({ name: 'CustomerBusinessTpe' })

withDefaults(defineProps<Props>(), {
  placeholder: '请选择客户业务类型',
})

interface Props {
  placeholder?: string
}
const {customer_business_type} = useDict('customer_business_type')


</script>

<template>
  <a-select
      :placeholder="placeholder"
      :options="customer_business_type"
      allow-clear
      allow-search
      style="width: 150px"
  />
</template>

<style scoped lang="scss">

</style>
